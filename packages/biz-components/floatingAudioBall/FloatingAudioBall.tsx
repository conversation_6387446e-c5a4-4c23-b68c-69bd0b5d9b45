import { Music } from '@bookln/iconsax';
import { useWindowDimensions } from '@jgl/biz-func';
import { useCallback, useMemo, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import type { FloatingAudioBallProps } from './types';

const BALL_SIZE = 60;
const BALL_RADIUS = BALL_SIZE / 2;
const ANIMATION_DURATION = 300;

export const FloatingAudioBall = (props: FloatingAudioBallProps) => {
  const { audioInfo, onHide } = props;

  const [isPlaying, setIsPlaying] = useState(true);

  // 使用shared value来控制展开状态，避免React重渲染
  const isExpanded = useSharedValue(0); // 0 = 收起, 1 = 展开
  const translateX = useSharedValue(10);
  const translateY = useSharedValue(300);
  const context = useSharedValue({ x: 0, y: 0 });

  const { width: screenWidth } = useWindowDimensions();

  const expandedWidth = useMemo(() => {
    return screenWidth * 0.8;
  }, [screenWidth]);

  const toggleExpand = useCallback(() => {
    isExpanded.value = withTiming(isExpanded.value === 0 ? 1 : 0, {
      duration: ANIMATION_DURATION,
    });
  }, [isExpanded]);

  const panGesture = useMemo(() => {
    return Gesture.Pan()
      .onStart(() => {
        context.value = { x: translateX.value, y: translateY.value };
      })
      .onUpdate((event) => {
        translateX.value = context.value.x + event.translationX;
        translateY.value = context.value.y + event.translationY;
      })
      .onEnd(() => {
        // 使用interpolate来获取当前宽度
        const currentWidth = interpolate(
          isExpanded.value,
          [0, 1],
          [BALL_SIZE, expandedWidth],
        );
        if (translateX.value > screenWidth / 2 - currentWidth / 2) {
          translateX.value = withTiming(screenWidth - currentWidth - 10, {
            duration: ANIMATION_DURATION,
          });
        } else {
          translateX.value = withTiming(10, {
            duration: ANIMATION_DURATION,
          });
        }
      });
  }, [context, expandedWidth, isExpanded, screenWidth, translateX, translateY]);

  const tapGesture = useMemo(() => {
    return Gesture.Tap().onEnd(() => {
      runOnJS(toggleExpand)();
    });
  }, [toggleExpand]);

  /** 组合手势：如果拖拽失败（即被识别为点击），则执行点击手势 */
  const composedGesture = useMemo(() => {
    return Gesture.Race(panGesture, tapGesture);
  }, [panGesture, tapGesture]);

  const animatedStyle = useAnimatedStyle(() => {
    const width = interpolate(
      isExpanded.value,
      [0, 1],
      [BALL_SIZE, expandedWidth],
    );

    return {
      width,
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  }, [expandedWidth]);

  // 收起状态的内容动画
  const collapsedContentStyle = useAnimatedStyle(() => {
    const opacity = interpolate(isExpanded.value, [0, 0.3, 1], [1, 0, 0]);
    const scale = interpolate(isExpanded.value, [0, 1], [1, 0.8]);

    return {
      opacity,
      transform: [{ scale }],
    };
  });

  // 展开状态的内容动画
  const expandedContentStyle = useAnimatedStyle(() => {
    const opacity = interpolate(isExpanded.value, [0, 0.7, 1], [0, 0, 1]);
    const translateX = interpolate(isExpanded.value, [0, 1], [20, 0]);

    return {
      opacity,
      transform: [{ translateX }],
    };
  });

  const handleClose = () => {
    if (isExpanded.value > 0.5) {
      // 如果当前是展开状态，则收起
      isExpanded.value = withTiming(0, { duration: ANIMATION_DURATION });
    } else {
      // 如果当前是收起状态，则隐藏整个球
      onHide?.();
    }
  };

  return (
    <GestureDetector gesture={composedGesture}>
      <Animated.View style={[styles.container, animatedStyle]}>
        {/* 收起状态 */}
        <Animated.View
          style={[
            styles.content,
            styles.collapsedContent,
            collapsedContentStyle,
          ]}
        >
          <Text style={styles.icon}>
            <Music variant={'Bold'} color={'#FF6AA6'} />
          </Text>
        </Animated.View>

        {/* 展开状态 */}
        <Animated.View
          style={[styles.content, styles.expandedContent, expandedContentStyle]}
        >
          <Text style={styles.title} numberOfLines={1}>
            {audioInfo?.title}
          </Text>
          <View style={styles.controls}>
            <TouchableOpacity onPress={() => setIsPlaying((p) => !p)}>
              <Text style={styles.icon}>{isPlaying ? '⏸️' : '▶️'}</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleClose}>
              <Text style={styles.icon}>❌</Text>
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    height: BALL_SIZE,
    borderRadius: BALL_RADIUS,
    backgroundColor: 'white',
    shadowColor: 'black',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  content: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  collapsedContent: {},
  expandedContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  icon: {
    fontSize: 24,
    color: 'white',
  },
  title: {
    flex: 1,
    color: 'white',
    fontSize: 16,
    marginRight: 10,
  },
  controls: {
    flexDirection: 'row',
    gap: 15, // 使用 gap 属性简化间距
  },
});
